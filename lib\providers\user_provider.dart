// lib/providers/user_provider.dart
import 'package:get/get.dart';
import '../models/user_models.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class UserProvider extends GetxController {
  // Variables observables
  final Rx<Usuario?> _currentUser = Rx<Usuario?>(null);
  final RxList<Empresa> _empresas = <Empresa>[].obs;
  final RxString _accessToken = ''.obs;
  final RxString _refreshToken = ''.obs;
  final RxBool _isLoggedIn = false.obs;
  final RxString _baseUrl = ''.obs;

  // Getters
  Usuario? get currentUser => _currentUser.value;
  List<Empresa> get empresas => _empresas;
  String get accessToken => _accessToken.value;
  String get refreshToken => _refreshToken.value;
  bool get isLoggedIn => _isLoggedIn.value;
  String get baseUrl => _baseUrl.value;

  // Constantes para SharedPreferences
  static const String _userKey = 'user_data';
  static const String _empresasKey = 'empresas_data';
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _baseUrlKey = 'connection_url';

  // Método para establecer los datos del login
  void setLoginData(LoginResponse loginResponse, String baseUrl) {
    _currentUser.value = loginResponse.user;
    _empresas.assignAll(loginResponse.empresas);
    _accessToken.value = loginResponse.accessToken;
    _refreshToken.value = loginResponse.refreshToken;
    _baseUrl.value = baseUrl;
    _isLoggedIn.value = true;

    // Guardar datos en SharedPreferences
    _saveUserData();
  }

  // Método para cerrar sesión
  void logout() {
    _currentUser.value = null;
    _empresas.clear();
    _accessToken.value = '';
    _refreshToken.value = '';
    _isLoggedIn.value = false;

    // Limpiar datos de SharedPreferences
    _clearUserData();
  }

  // Guardar datos en SharedPreferences
  Future<void> _saveUserData() async {
    final prefs = await SharedPreferences.getInstance();

    if (currentUser != null) {
      await prefs.setString(_userKey, jsonEncode(currentUser!.toJson()));
    }

    await prefs.setString(
      _empresasKey,
      jsonEncode(_empresas.map((e) => e.toJson()).toList()),
    );
    await prefs.setString(_accessTokenKey, _accessToken.value);
    await prefs.setString(_refreshTokenKey, _refreshToken.value);
  }

  // Método para establecer la URL base
  void setBaseUrl(String url) {
    _baseUrl.value = url;
  }

  // Cargar datos desde SharedPreferences
  Future<void> loadUserData() async {
    final prefs = await SharedPreferences.getInstance();

    final userJson = prefs.getString(_userKey);
    final empresasJson = prefs.getString(_empresasKey);
    final token = prefs.getString(_accessTokenKey);
    final refreshToken = prefs.getString(_refreshTokenKey);
    final baseUrl = prefs.getString(_baseUrlKey);

    if (userJson != null) {
      _currentUser.value = Usuario.fromJson(jsonDecode(userJson));
    }

    if (empresasJson != null) {
      final List<dynamic> empresasList = jsonDecode(empresasJson);
      _empresas.assignAll(
        empresasList.map((e) => Empresa.fromJson(e)).toList(),
      );
    }

    if (token != null && token.isNotEmpty) {
      _accessToken.value = token;
      _isLoggedIn.value = true;
    }

    if (refreshToken != null) {
      _refreshToken.value = refreshToken;
    }

    if (baseUrl != null) {
      _baseUrl.value = baseUrl;
    }
  }

  // Limpiar datos de SharedPreferences
  Future<void> _clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userKey);
    await prefs.remove(_empresasKey);
    await prefs.remove(_accessTokenKey);
    await prefs.remove(_refreshTokenKey);
  }
}
