import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/history_model.dart';
import '../models/bot_model.dart';
import '../providers/history_provider.dart';
import '../providers/bot_provider.dart';
import '../services/favorites_service.dart';
import '../pages/chat_page.dart';

class FavoritesController extends GetxController {
  final FavoritesService _favoritesService = Get.find<FavoritesService>();
  final HistoryProvider _historyProvider = Get.find<HistoryProvider>();
  final BotProvider _botProvider = Get.find<BotProvider>();

  final RxList<ChatHistory> _favoriteMessages = <ChatHistory>[].obs;
  final RxList<Bot> _availableBots = <Bot>[].obs;
  final RxBool _isLoading = false.obs;
  final RxString _error = ''.obs;
  final RxString _selectedBotFilter = ''.obs; // '' significa todos los bots

  // Getters
  List<ChatHistory> get favoriteMessages => _favoriteMessages;
  List<ChatHistory> get filteredFavoriteMessages {
    if (_selectedBotFilter.value.isEmpty) {
      return _favoriteMessages;
    }
    return _favoriteMessages.where((message) {
      return message.usuarioDestino == _selectedBotFilter.value;
    }).toList();
  }

  List<Bot> get availableBots => _availableBots;
  bool get isLoading => _isLoading.value;
  String get error => _error.value;
  String get selectedBotFilter => _selectedBotFilter.value;

  @override
  void onInit() {
    super.onInit();
    _initializeFavorites();
  }

  /// Inicializa la carga de favoritos
  Future<void> _initializeFavorites() async {
    _isLoading.value = true;
    try {
      // Los bots ya deberían estar cargados, pero por seguridad los obtenemos
      await loadAvailableBots();
      await loadFavoriteMessages();
    } finally {
      _isLoading.value = false;
    }
  }

  /// Carga todos los bots disponibles para el filtro
  Future<void> loadAvailableBots() async {
    try {
      final bots = await _botProvider.getBots();
      _availableBots.assignAll(bots);
    } catch (e) {
      _error.value = 'Error al cargar bots: $e';
    }
  }

  /// Carga todos los mensajes favoritos desde el historial
  Future<void> loadFavoriteMessages() async {
    try {
      _favoriteMessages.clear();

      // Obtener los IDs de mensajes favoritos
      final favoriteIds = _favoritesService.favoriteMessages;

      if (favoriteIds.isEmpty) {
        return;
      }

      // Buscar los mensajes favoritos en todo el historial de todos los bots
      final allHistory = <ChatHistory>[];

      // Obtener historial de todos los bots ya cargados
      for (final bot in _availableBots) {
        final botHistory = _historyProvider.getHistoryForBot(bot.id.toString());
        allHistory.addAll(botHistory);
      }

      // Filtrar solo los mensajes que están en favoritos
      final favoriteMessages = allHistory.where((message) {
        return favoriteIds.contains(message.rid);
      }).toList();

      // Ordenar por fecha (más recientes primero)
      favoriteMessages.sort(
        (a, b) => b.fechaRegistro.compareTo(a.fechaRegistro),
      );

      _favoriteMessages.assignAll(favoriteMessages);
    } catch (e) {
      _error.value = 'Error al cargar favoritos: $e';
    }
  }

  /// Actualiza el filtro de bot seleccionado
  void updateBotFilter(String botId) {
    _selectedBotFilter.value = botId;
  }

  /// Limpia el filtro de bot
  void clearBotFilter() {
    _selectedBotFilter.value = '';
  }

  /// Elimina un mensaje de favoritos
  Future<void> removeFavorite(ChatHistory message, BuildContext context) async {
    try {
      await _favoritesService.toggleFavorite(message, context);
      // Recargar la lista de favoritos
      await loadFavoriteMessages();
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error al eliminar favorito: $e')),
        );
      }
    }
  }

  /// Refresca la lista de favoritos
  Future<void> refreshFavorites() async {
    await _initializeFavorites();
  }

  /// Navega al chat y reenvía el mensaje
  Future<void> navigateToMessageAndResend(
    ChatHistory message,
    BuildContext context,
  ) async {
    try {
      // Buscar el bot correspondiente al mensaje
      final bot = _availableBots.firstWhereOrNull(
        (bot) =>
            bot.botId == message.usuarioDestino ||
            bot.id.toString() == message.usuarioDestino,
      );

      if (bot == null) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No se pudo encontrar el bot correspondiente'),
            ),
          );
        }
        return;
      }

      // Establecer el bot actual en el HistoryProvider
      _historyProvider.currentBotId = bot.id.toString();

      // Navegar a la página de chat con el mensaje a reenviar
      await Get.to(() => ChatPage(bot: bot, messageToResend: message.mensaje));

      // Mostrar confirmación de que se navegó al chat
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Navegando al chat...'),
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error al navegar al chat: $e')));
      }
    }
  }

  /// Verifica si un mensaje es favorito
  bool isFavorite(int messageRid) {
    return _favoritesService.isFavorite(messageRid);
  }

  /// Obtiene el nombre del bot por su ID
  String getBotNameById(String botId) {
    final bot = _availableBots.firstWhereOrNull(
      (bot) => bot.botId == botId || bot.id.toString() == botId,
    );
    return bot?.name ?? 'Bot desconocido ($botId)';
  }
}
