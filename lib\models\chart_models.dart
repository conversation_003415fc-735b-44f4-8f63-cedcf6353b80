class ChartDataset {
  final List<double> data;
  final String color;
  final double strokeWidth;
  final String label;

  ChartDataset({
    required this.data,
    required this.color,
    this.strokeWidth = 2.0,
    required this.label,
  });
}

class ChartData {
  final List<String> labels;
  final List<ChartDataset> datasets;
  final List<String> legend;

  ChartData({
    required this.labels,
    required this.datasets,
    required this.legend,
  });

  bool get isEmpty => labels.isEmpty || datasets.isEmpty;
}

class HeatMapData {
  final List<String> verticalLabels;
  final List<String> horizontalLabels;
  final List<List<double>> data;

  HeatMapData({
    required this.verticalLabels,
    required this.horizontalLabels,
    required this.data,
  });

  bool get isEmpty =>
      verticalLabels.isEmpty || horizontalLabels.isEmpty || data.isEmpty;
}

class ProcessedChartData {
  final ChartData bar;
  final ChartData line;
  final HeatMapData? heat;

  ProcessedChartData({required this.bar, required this.line, this.heat});

  static ProcessedChartData empty() {
    final emptyData = ChartData(labels: [], datasets: [], legend: []);
    return ProcessedChartData(bar: emptyData, line: emptyData, heat: null);
  }
}

class MetricData {
  final double total;
  final int count;

  MetricData({required this.total, required this.count});

  double get average => count > 0 ? total / count : 0.0;
}

enum ChartType { bar, line, heat }
