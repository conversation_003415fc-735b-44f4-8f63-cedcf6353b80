class ChatHistory {
  final int id;
  final int rid;
  final String usuario;
  final String usuarioDestino;
  final String mensaje;
  final DateTime fechaRegistro;
  final String mensajeTipo;
  final dynamic respuesta;

  ChatHistory({
    required this.id,
    required this.rid,
    required this.usuario,
    required this.usuarioDestino,
    required this.mensaje,
    required this.fechaRegistro,
    required this.mensajeTipo,
    required this.respuesta,
  });

  factory ChatHistory.fromJson(Map<String, dynamic> json) {
    return ChatHistory(
      id: json['ID'] as int,
      rid: json['RID'] as int,
      usuario: json['Usuario'] as String,
      usuarioDestino: json['UsuarioDestino'] as String,
      mensaje: json['Mensaje'] as String,
      fechaRegistro: DateTime.parse(json['FechaRegistro'] as String),
      mensajeTipo: json['MensajeTipo'] as String,
      respuesta: json['Respuesta'],
    );
  }
}
