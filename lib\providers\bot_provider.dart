import 'package:dio/dio.dart';
import 'package:get/get.dart' hide FormData, MultipartFile, Response;
import '../models/bot_model.dart';
import '../constants/api_endpoints.dart';
import 'user_provider.dart';

class BotProvider {
  late final Dio _dio;
  final UserProvider _userProvider = Get.find<UserProvider>();

  BotProvider() {
    _dio = Dio();
    _dio.options.connectTimeout = const Duration(seconds: 10);
    _dio.options.receiveTimeout = const Duration(seconds: 10);
  }

  /// Obtiene la lista de bots disponibles para el usuario
  Future<List<Bot>> getBots() async {
    try {
      // Verificar que el usuario esté autenticado
      if (!_userProvider.isLoggedIn || _userProvider.accessToken.isEmpty) {
        throw Exception('Usuario no autenticado');
      }

      // Obtener la URL base guardada en el provider
      final baseUrl = _userProvider.baseUrl;
      if (baseUrl.isEmpty) {
        throw Exception('URL del servidor no configurada');
      }

      // Construir la URL completa
      final url = '$baseUrl${ApiEndpoints.botsEndpoint}';

      // Configurar headers con el token
      final headers = {'Authorization': 'Bearer ${_userProvider.accessToken}'};

      // Realizar la petición
      final response = await _dio.post(url, options: Options(headers: headers));

      // Verificar el código de estado
      if (response.statusCode == 200) {
        // Parsear la respuesta
        final List<dynamic> botsJson = response.data;
        return botsJson.map((json) => Bot.fromJson(json)).toList();
      } else {
        throw Exception('Error en el servidor: ${response.statusCode}');
      }
    } on DioException catch (e) {
      throw _handleDioError(e);
    } catch (e) {
      throw Exception('Error inesperado: $e');
    }
  }

  /// Maneja los errores de Dio
  Exception _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return Exception('Tiempo de conexión agotado');
      case DioExceptionType.receiveTimeout:
        return Exception('Tiempo de respuesta agotado');
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['message'] ?? 'Error del servidor';
        return Exception('Error $statusCode: $message');
      case DioExceptionType.cancel:
        return Exception('Petición cancelada');
      case DioExceptionType.connectionError:
        return Exception('Error de conexión. Verifique su conexión a internet');
      default:
        return Exception('Error de red: ${e.message}');
    }
  }

  /// Cierra el cliente Dio
  void dispose() {
    _dio.close();
  }
}
