class Sucursal {
  final int sucursal;
  final String sucursalNombre;

  Sucursal({required this.sucursal, required this.sucursalNombre});

  factory Sucursal.fromJson(Map<String, dynamic> json) {
    return Sucursal(
      sucursal: json['Sucursal'] as int,
      sucursalNombre: json['SucursalNombre'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'Sucursal': sucursal, 'SucursalNombre': sucursalNombre};
  }
}

class Empresa {
  final String empresa;
  final List<Sucursal> sucursales;

  Empresa({required this.empresa, required this.sucursales});

  factory Empresa.fromJson(Map<String, dynamic> json) {
    return Empresa(
      empresa: json['Empresa'] as String,
      sucursales: (json['Sucursales'] as List<dynamic>)
          .map(
            (sucursalJson) =>
                Sucursal.fromJson(sucursalJson as Map<String, dynamic>),
          )
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Empresa': empresa,
      'Sucursales': sucursales.map((sucursal) => sucursal.toJson()).toList(),
    };
  }
}

class Usuario {
  final String usuario;
  final String nombre;
  final int sucursal;

  Usuario({
    required this.usuario,
    required this.nombre,
    required this.sucursal,
  });

  factory Usuario.fromJson(Map<String, dynamic> json) {
    return Usuario(
      usuario: json['Usuario'] as String,
      nombre: json['Nombre'] as String,
      sucursal: json['Sucursal'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {'Usuario': usuario, 'Nombre': nombre, 'Sucursal': sucursal};
  }
}

class LoginData {
  final Usuario user;
  final List<Empresa> empresas;
  final String accessToken;
  final String refreshToken;

  LoginData({
    required this.user,
    required this.empresas,
    required this.accessToken,
    required this.refreshToken,
  });

  factory LoginData.fromJson(Map<String, dynamic> json) {
    return LoginData(
      user: Usuario.fromJson(json['user'] as Map<String, dynamic>),
      empresas: (json['empresas'] as List<dynamic>)
          .map(
            (empresaJson) =>
                Empresa.fromJson(empresaJson as Map<String, dynamic>),
          )
          .toList(),
      accessToken: json['access_token'] as String,
      refreshToken: json['refresh_token'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user': user.toJson(),
      'empresas': empresas.map((empresa) => empresa.toJson()).toList(),
      'access_token': accessToken,
      'refresh_token': refreshToken,
    };
  }
}

class LoginResponse {
  final String status;
  final LoginData data;

  LoginResponse({required this.status, required this.data});

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      status: json['status'] as String,
      data: LoginData.fromJson(json['data'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {'status': status, 'data': data.toJson()};
  }

  /// Verifica si el login fue exitoso
  bool get isSuccess => status == 'success';

  /// Getters de conveniencia
  String get accessToken => data.accessToken;
  String get refreshToken => data.refreshToken;
  Usuario get user => data.user;
  List<Empresa> get empresas => data.empresas;
}
