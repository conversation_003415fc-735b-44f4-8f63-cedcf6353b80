import 'package:flutter/material.dart';

class Bot {
  final int id;
  final String botId;
  final String name;
  final String type;
  final DateTime registrationDate;
  final IconData icon;

  Bot({
    required this.id,
    required this.botId,
    required this.name,
    required this.type,
    required this.registrationDate,
    this.icon = Icons.smart_toy_outlined,
  });

  factory Bot.fromJson(Map<String, dynamic> json) {
    return Bot(
      id: json['ID'] as int,
      botId: json['Bot'] as String,
      name: json['Nombre'] as String,
      type: json['Tipo'] as String,
      registrationDate: DateTime.parse(json['FechaRegistro'] as String),
      // Asignar icono según el tipo
      icon: _getIconForType(json['Tipo'] as String),
    );
  }

  static IconData _getIconForType(String type) {
    switch (type.toLowerCase()) {
      case 'sql-query':
        return Icons.smart_toy_outlined;
      case 'chatgpt':
        return Icons.chat_outlined;
      default:
        return Icons.smart_toy_outlined;
    }
  }
}
