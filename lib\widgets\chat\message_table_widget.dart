import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../../models/history_model.dart';
import '../../utils/response_formatter.dart';

class MessageTableWidget extends StatelessWidget {
  final ChatHistory message;

  const MessageTableWidget({
    super.key,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    try {
      final List<Map<String, dynamic>> data = ResponseFormatter.formatResponse(
        message,
      );

      if (data.isEmpty) {
        return const Text('No hay datos para mostrar en la tabla');
      }

      // Obtener las columnas de la primera fila
      final List<String> columns = data.first.keys.toList();

      // Crear columnas de la tabla
      final List<DataColumn> dataColumns = columns
          .map(
            (column) => DataColumn(
              label: Text(
                column,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          )
          .toList();

      // Limitar a 10 filas para la visualización
      final int totalRows = data.length;
      final int visibleRows = totalRows > 10 ? 10 : totalRows;

      // Crear filas de la tabla (limitadas a 10)
      final List<DataRow> dataRows = data
          .take(visibleRows)
          .map(
            (row) => DataRow(
              cells: columns
                  .map(
                    (column) => DataCell(Text(row[column]?.toString() ?? '')),
                  )
                  .toList(),
            ),
          )
          .toList();

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: DataTable(columns: dataColumns, rows: dataRows),
          ),

          // Mostrar indicador si hay más filas
          if (totalRows > 10)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                'Mostrando 10 de $totalRows filas',
                style: TextStyle(
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                  color: theme.colorScheme.onSecondaryContainer.withValues(
                    alpha: 0.7,
                  ),
                ),
              ),
            ),
        ],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error al construir tabla: $e');
        print('Tipo de mensaje: ${message.mensajeTipo}');
        print('Tipo de respuesta: ${message.respuesta.runtimeType}');
        print('Contenido de respuesta: ${message.respuesta}');
      }

      return Text(
        'Error al mostrar la tabla: $e',
        style: TextStyle(color: theme.colorScheme.error),
      );
    }
  }
}
