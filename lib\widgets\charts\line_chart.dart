import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../models/chart_models.dart';

class LineChart extends StatelessWidget {
  final ChartData data;
  final double width;
  final double height;
  final bool showDataPoints;
  final bool showDataValues;
  final double strokeWidth;
  final double horizontalLabelRotation;

  const LineChart({
    super.key,
    required this.data,
    required this.width,
    required this.height,
    this.showDataPoints = true,
    this.showDataValues = false,
    this.strokeWidth = 2.0,
    this.horizontalLabelRotation = 0,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (data.isEmpty) {
      return SizedBox(
        width: width,
        height: height,
        child: Center(
          child: Text(
            'No hay datos para mostrar',
            style: theme.textTheme.bodyMedium,
          ),
        ),
      );
    }

    // Calcular altura disponible para la gráfica (reservar espacio para leyenda)
    const legendSpacing = 12.0;
    // Calcular altura dinámica de la leyenda basada en el número de elementos
    final legendItemsPerRow = (width / 120).floor().clamp(
      1,
      data.legend.length,
    );
    final legendRows = (data.legend.length / legendItemsPerRow).ceil();
    final legendHeight =
        (legendRows * 24.0) +
        ((legendRows - 1) * 8.0); // 24px por fila + 8px de spacing
    final chartHeight = height - legendSpacing - legendHeight;

    return SizedBox(
      width: width,
      height: height,
      child: Column(
        children: [
          CustomPaint(
            size: Size(width, chartHeight),
            painter: _LineChartPainter(
              data: data,
              theme: theme,
              showDataPoints: showDataPoints,
              showDataValues: showDataValues,
              strokeWidth: strokeWidth,
              horizontalLabelRotation: horizontalLabelRotation,
            ),
          ),
          const SizedBox(height: legendSpacing),
          _buildLegend(theme),
        ],
      ),
    );
  }

  Widget _buildLegend(ThemeData theme) {
    return Wrap(
      alignment: WrapAlignment.center,
      spacing: 16,
      runSpacing: 8,
      children: data.legend.asMap().entries.map((entry) {
        final index = entry.key;
        final label = entry.value;
        final color = _parseColor(data.datasets[index].color);

        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 24),
            Container(
              width: 16,
              height: 3,
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(1.5),
              ),
            ),
            const SizedBox(width: 4),
            Text(label, style: theme.textTheme.bodySmall),
          ],
        );
      }).toList(),
    );
  }

  Color _parseColor(String colorString) {
    // Remover el # si existe
    String hexColor = colorString.replaceAll('#', '');

    // Agregar alpha si no está presente
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }

    return Color(int.parse(hexColor, radix: 16));
  }
}

class _LineChartPainter extends CustomPainter {
  final ChartData data;
  final ThemeData theme;
  final bool showDataPoints;
  final bool showDataValues;
  final double strokeWidth;
  final double horizontalLabelRotation;

  static const double padding = 20;
  static const double labelSpacingFromChart = 12;
  static const double topPadding = 20;
  static const double yAxisWidth = 60; // Espacio para el eje Y
  static const double pointRadius = 4.0;

  _LineChartPainter({
    required this.data,
    required this.theme,
    required this.showDataPoints,
    required this.showDataValues,
    required this.strokeWidth,
    required this.horizontalLabelRotation,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    // Calcular dimensiones basado en etiquetas truncadas
    final maxTruncatedLength = _getMaxTruncatedLabelLength();
    final extraHeightForLongLabels = math.max(
      0,
      (maxTruncatedLength - 10) * 1.5,
    );
    final rotationFactor = horizontalLabelRotation.abs() > 0
        ? 2.5
        : 1; // Más espacio para rotación
    final axisHeight =
        labelSpacingFromChart + (extraHeightForLongLabels * rotationFactor);
    final bottomPadding = padding + axisHeight;
    final chartHeight = size.height - topPadding - bottomPadding;

    // Obtener valor máximo y mínimo
    final allValues = data.datasets.expand((ds) => ds.data).toList();
    if (allValues.isEmpty) return;

    final maxValue = allValues.reduce(math.max);
    final minValue = allValues.reduce(math.min);
    final adjustedMaxValue = maxValue == minValue ? maxValue + 1 : maxValue;
    final adjustedMinValue = maxValue == minValue ? minValue - 1 : minValue;

    // Calcular dimensiones del área de gráfica
    final chartAreaWidth = size.width - yAxisWidth - padding;
    final pointSpacing = data.labels.length > 1
        ? chartAreaWidth / (data.labels.length - 1)
        : chartAreaWidth / 2;

    // Dibujar eje Y con valores de referencia
    _drawYAxis(
      canvas,
      size,
      chartHeight,
      adjustedMaxValue,
      adjustedMinValue,
      bottomPadding,
    );

    // Dibujar líneas
    _drawLines(
      canvas,
      size,
      chartHeight,
      adjustedMaxValue,
      adjustedMinValue,
      pointSpacing,
      bottomPadding,
    );

    // Dibujar etiquetas del eje X
    _drawXAxisLabels(canvas, size, pointSpacing, bottomPadding);
  }

  void _drawLines(
    Canvas canvas,
    Size size,
    double chartHeight,
    double maxValue,
    double minValue,
    double pointSpacing,
    double bottomPadding,
  ) {
    final valueRange = maxValue - minValue;

    for (
      int datasetIndex = 0;
      datasetIndex < data.datasets.length;
      datasetIndex++
    ) {
      final dataset = data.datasets[datasetIndex];
      final color = _parseColor(dataset.color);

      // Preparar paint para la línea
      final linePaint = Paint()
        ..color = color
        ..strokeWidth = strokeWidth
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round
        ..strokeJoin = StrokeJoin.round;

      // Preparar paint para los puntos
      final pointPaint = Paint()
        ..color = color
        ..style = PaintingStyle.fill;

      // Crear path para la línea
      final path = Path();
      final points = <Offset>[];

      for (int i = 0; i < dataset.data.length; i++) {
        final value = dataset.data[i];
        final x = yAxisWidth + (i * pointSpacing);
        final y = valueRange == 0
            ? topPadding + chartHeight / 2
            : topPadding +
                  chartHeight -
                  ((value - minValue) / valueRange) * chartHeight;

        final point = Offset(x, y);
        points.add(point);

        if (i == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }

      // Dibujar la línea
      canvas.drawPath(path, linePaint);

      // Dibujar puntos de datos si está habilitado
      if (showDataPoints) {
        for (final point in points) {
          canvas.drawCircle(point, pointRadius, pointPaint);

          // Dibujar borde blanco alrededor del punto
          final borderPaint = Paint()
            ..color = theme.colorScheme.surface
            ..strokeWidth = 1.5
            ..style = PaintingStyle.stroke;
          canvas.drawCircle(point, pointRadius, borderPaint);
        }
      }

      // Dibujar valores si está habilitado
      if (showDataValues) {
        for (int i = 0; i < points.length; i++) {
          final point = points[i];
          final value = dataset.data[i];
          _drawValueLabel(canvas, point.dx, point.dy - 15, value);
        }
      }
    }
  }

  void _drawXAxisLabels(
    Canvas canvas,
    Size size,
    double pointSpacing,
    double bottomPadding,
  ) {
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    for (int i = 0; i < data.labels.length; i++) {
      final label = data.labels[i];
      final x = yAxisWidth + (i * pointSpacing);
      final labelY = size.height - (labelSpacingFromChart - 2);

      // Truncar etiquetas largas (más espacio con rotación)
      final displayLabel = _truncateLabel(
        label,
        horizontalLabelRotation.abs() > 0 ? 20 : 15,
      );

      textPainter.text = TextSpan(
        text: displayLabel,
        style: theme.textTheme.bodySmall?.copyWith(
          fontSize: 10,
          color: theme.colorScheme.onSurface,
        ),
      );

      textPainter.layout();

      canvas.save();

      if (horizontalLabelRotation != 0) {
        canvas.translate(x, labelY);
        canvas.rotate(horizontalLabelRotation * math.pi / 180);
        textPainter.paint(
          canvas,
          Offset(-textPainter.width / 2, -textPainter.height / 2),
        );
      } else {
        textPainter.paint(
          canvas,
          Offset(x - textPainter.width / 2, labelY - textPainter.height),
        );
      }

      canvas.restore();
    }
  }

  void _drawValueLabel(Canvas canvas, double x, double y, double value) {
    final formattedValue = _formatNumber(value);

    final textPainter = TextPainter(
      text: TextSpan(
        text: formattedValue,
        style: theme.textTheme.bodySmall?.copyWith(
          fontSize: 9,
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.onSurface,
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    textPainter.layout();

    // Dibujar fondo para la etiqueta
    final backgroundRect = Rect.fromCenter(
      center: Offset(x, y),
      width: textPainter.width + 6,
      height: textPainter.height + 3,
    );

    final backgroundPaint = Paint()
      ..color = theme.colorScheme.surface.withValues(alpha: 0.9)
      ..style = PaintingStyle.fill;

    canvas.drawRRect(
      RRect.fromRectAndRadius(backgroundRect, const Radius.circular(3)),
      backgroundPaint,
    );

    // Dibujar texto
    textPainter.paint(
      canvas,
      Offset(x - textPainter.width / 2, y - textPainter.height / 2),
    );
  }

  String _formatNumber(double num) {
    final fixedNum = double.parse(num.toStringAsFixed(2));
    return fixedNum.toStringAsFixed(
      fixedNum.truncateToDouble() == fixedNum ? 0 : 2,
    );
  }

  String _truncateLabel(String label, int maxLength) {
    if (label.length <= maxLength) return label;

    // Intentar cortar en un espacio o guión para mejor legibilidad
    final truncated = label.substring(0, maxLength);
    final lastSpace = truncated.lastIndexOf(' ');
    final lastHyphen = truncated.lastIndexOf('-');
    final lastBreakPoint = math.max(lastSpace, lastHyphen);

    String result;
    // Si hay un punto de corte natural y no está muy al principio, usarlo
    if (lastBreakPoint > maxLength * 0.6) {
      result = label.substring(0, lastBreakPoint);
    } else {
      // Si no, corte directo
      result = truncated;
    }

    // Agregar elipsis visual si se truncó el texto
    return '$result...';
  }

  int _getMaxTruncatedLabelLength() {
    final maxLength = horizontalLabelRotation.abs() > 0 ? 20 : 15;
    return data.labels.fold<int>(0, (max, label) {
      if (label.length <= maxLength) return math.max(max, label.length);

      // Calcular la longitud real del texto truncado (sin los puntos suspensivos)
      final truncated = label.substring(0, maxLength);
      final lastSpace = truncated.lastIndexOf(' ');
      final lastHyphen = truncated.lastIndexOf('-');
      final lastBreakPoint = math.max(lastSpace, lastHyphen);

      int actualLength;
      if (lastBreakPoint > maxLength * 0.6) {
        actualLength = lastBreakPoint;
      } else {
        actualLength = maxLength;
      }

      return math.max(max, actualLength);
    });
  }

  Color _parseColor(String colorString) {
    String hexColor = colorString.replaceAll('#', '');
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }
    return Color(int.parse(hexColor, radix: 16));
  }

  void _drawYAxis(
    Canvas canvas,
    Size size,
    double chartHeight,
    double maxValue,
    double minValue,
    double bottomPadding,
  ) {
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Calcular valores de referencia para el eje Y
    final yValues = _calculateYAxisValues(maxValue, minValue);
    final valueRange = maxValue - minValue;

    for (final yValue in yValues) {
      final yPosition = valueRange == 0
          ? topPadding + chartHeight / 2
          : topPadding +
                chartHeight -
                ((yValue - minValue) / valueRange) * chartHeight;

      // Dibujar línea de referencia horizontal (opcional, sutil)
      final linePaint = Paint()
        ..color = theme.colorScheme.outline.withValues(alpha: 0.2)
        ..strokeWidth = 0.5;

      canvas.drawLine(
        Offset(yAxisWidth, yPosition),
        Offset(size.width, yPosition),
        linePaint,
      );

      // Dibujar valor en el eje Y
      textPainter.text = TextSpan(
        text: _formatYAxisValue(yValue),
        style: theme.textTheme.bodySmall?.copyWith(
          fontSize: 10,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
        ),
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          yAxisWidth - textPainter.width - 8,
          yPosition - textPainter.height / 2,
        ),
      );
    }
  }

  List<double> _calculateYAxisValues(double maxValue, double minValue) {
    if (maxValue == minValue) return [minValue];

    final range = maxValue - minValue;

    // Calcular un buen número de divisiones (4-6 líneas)
    final magnitude = math
        .pow(10, (math.log(range) / math.ln10).floor())
        .toDouble();
    final normalized = range / magnitude;

    double step;
    if (normalized <= 1) {
      step = magnitude * 0.2;
    } else if (normalized <= 2) {
      step = magnitude * 0.5;
    } else if (normalized <= 5) {
      step = magnitude;
    } else {
      step = magnitude * 2;
    }

    final values = <double>[];

    // Encontrar el primer valor que sea múltiplo del step y >= minValue
    final firstValue = (minValue / step).ceil() * step;

    double current = firstValue;
    while (current <= maxValue) {
      values.add(current);
      current += step;
    }

    // Asegurar que tenemos al menos el valor mínimo y máximo representados
    if (values.isEmpty || values.first > minValue) {
      values.insert(0, minValue);
    }
    if (values.last < maxValue * 0.95) {
      values.add(maxValue);
    }

    return values;
  }

  String _formatYAxisValue(double value) {
    if (value == 0) return '0';

    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(value % 1000000 == 0 ? 0 : 1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(value % 1000 == 0 ? 0 : 1)}K';
    } else if (value >= 1) {
      return value.toStringAsFixed(value % 1 == 0 ? 0 : 1);
    } else {
      return value.toStringAsFixed(2);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
