import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../models/chart_models.dart';

class HeatMap<PERSON>hart extends StatelessWidget {
  final HeatMapData data;
  final double width;
  final double height;
  final Function(String vertical, String horizontal, double value)? onCellTap;

  const HeatMapChart({
    super.key,
    required this.data,
    required this.width,
    required this.height,
    this.onCellTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (data.isEmpty) {
      return SizedBox(
        width: width,
        height: height,
        child: Center(
          child: Text(
            'No hay datos para mostrar',
            style: theme.textTheme.bodyMedium,
          ),
        ),
      );
    }

    // Calcular altura dinámica basada en la cantidad de filas
    const cellSize = 20.0;
    const cellSpacing = 4.0;
    const padding = 20.0;
    const horizontalLabelHeight = 60.0; // Aumentado para etiquetas más largas
    const legendHeight = 100.0; // Altura fija para la leyenda
    const legendSpacing = 12.0;

    final calculatedChartHeight =
        padding +
        horizontalLabelHeight +
        (data.verticalLabels.length * (cellSize + cellSpacing));

    final totalHeight = calculatedChartHeight + legendSpacing + legendHeight;

    // Si la altura calculada es mayor que la altura disponible, usar scroll vertical
    final useVerticalScroll = totalHeight > height;
    final finalChartHeight = useVerticalScroll
        ? calculatedChartHeight
        : height - legendSpacing - legendHeight;

    return SizedBox(
      width: width,
      height: height,
      child: Column(
        children: [
          // Área del gráfico con scroll vertical si es necesario
          Expanded(
            child: useVerticalScroll
                ? SingleChildScrollView(
                    child: CustomPaint(
                      size: Size(width, finalChartHeight),
                      painter: _HeatMapPainter(
                        data: data,
                        theme: theme,
                        onCellTap: onCellTap,
                      ),
                    ),
                  )
                : CustomPaint(
                    size: Size(width, finalChartHeight),
                    painter: _HeatMapPainter(
                      data: data,
                      theme: theme,
                      onCellTap: onCellTap,
                    ),
                  ),
          ),
          const SizedBox(height: legendSpacing),
          // Leyenda siempre visible en la parte inferior
          _buildLegend(theme),
        ],
      ),
    );
  }

  Widget _buildLegend(ThemeData theme) {
    // Calcular rangos dinámicos basados en los datos reales
    final colorLegendConfig = _calculateDynamicLegend();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        children: [
          Text(
            'Referencia de valores:',
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            alignment: WrapAlignment.center,
            spacing: 12,
            runSpacing: 8,
            children: colorLegendConfig.map((item) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: item['color'] as Color,
                      borderRadius: BorderRadius.circular(2),
                      border: Border.all(
                        color: theme.colorScheme.outline.withValues(alpha: 0.3),
                        width: 0.5,
                      ),
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    item['range'] as String,
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      fontSize: 10,
                    ),
                  ),
                ],
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  double _getMaxValue() {
    if (data.isEmpty) return 0;

    double maxValue = 0;
    for (final row in data.data) {
      for (final value in row) {
        if (value > maxValue) {
          maxValue = value;
        }
      }
    }
    return maxValue;
  }

  List<Map<String, dynamic>> _calculateDynamicLegend() {
    final maxValue = _getMaxValue();

    if (maxValue == 0) {
      return [
        {'range': '0', 'color': const Color(0xFFEBEDF0)},
      ];
    }

    // Calcular rangos dinámicos
    final step = maxValue / 4; // Dividir en 4 rangos + el 0

    return [
      {'range': '0', 'color': const Color(0xFFEBEDF0)},
      {'range': '1-${step.round()}', 'color': const Color(0xFFC6E48B)},
      {
        'range': '${(step + 1).round()}-${(step * 2).round()}',
        'color': const Color(0xFF7BC96F),
      },
      {
        'range': '${(step * 2 + 1).round()}-${(step * 3).round()}',
        'color': const Color(0xFF239A3B),
      },
      {'range': '${(step * 3 + 1).round()}+', 'color': const Color(0xFF196127)},
    ];
  }
}

class _HeatMapPainter extends CustomPainter {
  final HeatMapData data;
  final ThemeData theme;
  final Function(String vertical, String horizontal, double value)? onCellTap;

  static const double padding = 20;
  static const double cellSize = 20;
  static const double cellSpacing = 4;
  static const double labelSpacing = 8;
  static const double verticalLabelWidth = 80;

  _HeatMapPainter({required this.data, required this.theme, this.onCellTap});

  @override
  void paint(Canvas canvas, Size size) {
    final textPainter = TextPainter(textDirection: TextDirection.ltr);

    // Calcular dimensiones con más espacio para etiquetas horizontales
    final horizontalLabelHeight = 60; // Aumentado de 40 a 60
    final chartStartY = padding + horizontalLabelHeight;
    final chartStartX = padding + verticalLabelWidth;

    // Dibujar etiquetas horizontales
    _drawHorizontalLabels(canvas, textPainter, chartStartX, padding);

    // Dibujar etiquetas verticales y celdas
    _drawVerticalLabelsAndCells(canvas, textPainter, chartStartX, chartStartY);
  }

  void _drawHorizontalLabels(
    Canvas canvas,
    TextPainter textPainter,
    double chartStartX,
    double startY,
  ) {
    for (int i = 0; i < data.horizontalLabels.length; i++) {
      final originalLabel = data.horizontalLabels[i];
      final x = chartStartX + i * (cellSize + cellSpacing) + cellSize;

      // Truncar etiquetas muy largas para evitar que se salgan
      final maxLabelLength = 15; // Máximo 15 caracteres
      final label = originalLabel.length > maxLabelLength
          ? '${originalLabel.substring(0, maxLabelLength)}...'
          : originalLabel;

      textPainter.text = TextSpan(
        text: label,
        style: theme.textTheme.bodySmall?.copyWith(
          fontSize: 10,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
        ),
      );

      textPainter.layout();

      // Rotar el texto -45 grados y ajustar posición para mejor centrado
      canvas.save();
      canvas.translate(x, startY + 15); // Aumentar espacio vertical
      canvas.rotate(-math.pi / 4);
      // Centrar el texto rotado ajustando la posición
      textPainter.paint(
        canvas,
        Offset(-textPainter.width / 2, -textPainter.height / 2),
      );
      canvas.restore();
    }
  }

  void _drawVerticalLabelsAndCells(
    Canvas canvas,
    TextPainter textPainter,
    double chartStartX,
    double chartStartY,
  ) {
    for (int rowIndex = 0; rowIndex < data.verticalLabels.length; rowIndex++) {
      final rowLabel = data.verticalLabels[rowIndex];
      final y = chartStartY + rowIndex * (cellSize + cellSpacing);

      // Truncar etiquetas verticales muy largas
      final maxVerticalLabelLength =
          12; // Máximo 12 caracteres para etiquetas verticales
      final displayLabel = rowLabel.length > maxVerticalLabelLength
          ? '${rowLabel.substring(0, maxVerticalLabelLength)}...'
          : rowLabel;

      // Dibujar etiqueta vertical
      textPainter.text = TextSpan(
        text: displayLabel,
        style: theme.textTheme.bodySmall?.copyWith(
          fontSize: 10,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
        ),
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          chartStartX - textPainter.width - labelSpacing,
          y + (cellSize - textPainter.height) / 2,
        ),
      );

      // Dibujar celdas de la fila
      for (
        int colIndex = 0;
        colIndex < data.horizontalLabels.length;
        colIndex++
      ) {
        final value =
            rowIndex < data.data.length && colIndex < data.data[rowIndex].length
            ? data.data[rowIndex][colIndex]
            : 0.0;

        final cellX = chartStartX + colIndex * (cellSize + cellSpacing);
        final cellRect = Rect.fromLTWH(cellX, y, cellSize, cellSize);

        // Dibujar celda con color basado en valor
        final cellPaint = Paint()
          ..color = _getColorForValue(value)
          ..style = PaintingStyle.fill;

        canvas.drawRRect(
          RRect.fromRectAndRadius(cellRect, const Radius.circular(3)),
          cellPaint,
        );

        // Dibujar borde sutil
        final borderPaint = Paint()
          ..color = theme.colorScheme.outline.withValues(alpha: 0.1)
          ..strokeWidth = 0.5
          ..style = PaintingStyle.stroke;

        canvas.drawRRect(
          RRect.fromRectAndRadius(cellRect, const Radius.circular(3)),
          borderPaint,
        );
      }
    }
  }

  double? _cachedMaxValue;

  double _getMaxValue() {
    if (_cachedMaxValue != null) return _cachedMaxValue!;

    if (data.data.isEmpty) {
      _cachedMaxValue = 0;
      return 0;
    }

    double maxValue = 0;
    for (final row in data.data) {
      for (final val in row) {
        if (val > maxValue) {
          maxValue = val;
        }
      }
    }
    _cachedMaxValue = maxValue;
    return maxValue;
  }

  Color _getColorForValue(double value) {
    if (value == 0) {
      return const Color(0xFFEBEDF0); // Sin actividad
    }

    final maxValue = _getMaxValue();
    if (maxValue == 0) {
      return const Color(0xFFEBEDF0);
    }

    final step = maxValue / 4;

    if (value <= step) {
      return const Color(0xFFC6E48B); // Baja
    } else if (value <= step * 2) {
      return const Color(0xFF7BC96F); // Media-baja
    } else if (value <= step * 3) {
      return const Color(0xFF239A3B); // Media-alta
    }
    return const Color(0xFF196127); // Alta
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
