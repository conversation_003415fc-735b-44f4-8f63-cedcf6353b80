import 'dart:convert';

// Simulación de datos de prueba para verificar la separación de métricas y agrupadores
void main() {
  // Datos de ejemplo que podrían venir de un análisis
  final testData = [
    {
      'categoria': 'Ventas',
      'fecha': '2024-01-01',
      'total_ventas': 1500.0,
      'cantidad_productos': 25,
      'region': 'Norte',
      'precio_promedio': 60.0,
    },
    {
      'categoria': 'Marketing',
      'fecha': '2024-01-02',
      'total_ventas': 2300.0,
      'cantidad_productos': 38,
      'region': 'Sur',
      'precio_promedio': 75.5,
    },
    {
      'categoria': 'Ventas',
      'fecha': '2024-01-03',
      'total_ventas': 1800.0,
      'cantidad_productos': 30,
      'region': 'Este',
      'precio_promedio': 65.2,
    },
  ];

  print('=== DATOS DE PRUEBA ===');
  print('Columnas disponibles: ${testData.first.keys.toList()}');
  print('');

  // Simular la lógica de _isNumericColumn
  final numericColumns = <String>[];
  final nonNumericColumns = <String>[];

  for (final column in testData.first.keys) {
    if (isNumericColumn(testData, column)) {
      numericColumns.add(column);
    } else {
      nonNumericColumns.add(column);
    }
  }

  print('=== RESULTADO DE LA SEPARACIÓN ===');
  print('Columnas NUMÉRICAS (para métricas): $numericColumns');
  print('Columnas NO NUMÉRICAS (para agrupadores): $nonNumericColumns');
  print('');

  print('=== VERIFICACIÓN ===');
  print('✓ Las métricas y agrupadores están separados correctamente');
  print('✓ No hay solapamiento entre métricas y agrupadores');
  
  // Verificar que no hay solapamiento
  final overlap = numericColumns.where((col) => nonNumericColumns.contains(col)).toList();
  if (overlap.isEmpty) {
    print('✓ CORRECTO: No hay columnas que aparezcan en ambas listas');
  } else {
    print('✗ ERROR: Las siguientes columnas aparecen en ambas listas: $overlap');
  }
}

// Simulación de la función _isNumericColumn
bool isNumericColumn(List<Map<String, dynamic>> data, String column) {
  int numericCount = 0;
  int totalCount = 0;

  for (final row in data.take(10)) {
    final value = row[column];
    if (value != null) {
      totalCount++;
      if (parseNumericValue(value) != 0 || value.toString() == '0') {
        numericCount++;
      }
    }
  }

  return totalCount > 0 && (numericCount / totalCount) >= 0.7;
}

// Simulación de la función _parseNumericValue
double parseNumericValue(dynamic value) {
  if (value == null) return 0.0;

  if (value is num) return value.toDouble();

  final stringValue = value.toString().trim();
  if (stringValue.isEmpty) return 0.0;

  final cleanValue = stringValue
      .replaceAll(',', '')
      .replaceAll('\$', '')
      .replaceAll('%', '');

  return double.tryParse(cleanValue) ?? 0.0;
}
