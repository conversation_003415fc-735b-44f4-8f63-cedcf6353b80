import 'package:get/get.dart';
import 'favorites_service.dart';
import 'chat_service.dart';
import 'scheduler_service.dart';

class ServiceLocator {
  static void init() {
    // Registrar servicios globales
    if (!Get.isRegistered<FavoritesService>()) {
      Get.put(FavoritesService(), permanent: true);
    }
    if (!Get.isRegistered<ChatService>()) {
      Get.put(ChatService(), permanent: true);
    }
    if (!Get.isRegistered<SchedulerService>()) {
      Get.put(SchedulerService(), permanent: true);
    }
  }
}
