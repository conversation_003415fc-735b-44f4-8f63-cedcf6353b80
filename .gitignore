# Archivos y directorios generados por Dart y Flutter
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
build/
# pubspec.lock es generado por 'flutter pub get'.
# Generalmente, DEBERÍAS versionar pubspec.lock para asegurar builds consistentes.
# Descomenta la siguiente línea si NO quieres versionarlo (no recomendado para aplicaciones).
# pubspec.lock

# Archivos de configuración de IDEs
.idea/
.vscode/
*.iml
*.project
*.classpath
*.settings/

# Archivos de sistema operativo
.DS_Store
Thumbs.db
ehthumbs.db

# Archivos de log
*.log

# Archivos de compilación de plataformas específicas
# Android
*.apk
*.aab
*.keystore
*.jks
# No versionar google-services.json si contiene claves sensibles y no es para CI/CD público
# google-services.json

# iOS
*.ipa
*.app
*.dSYM/
# No versionar GoogleService-Info.plist si contiene claves sensibles y no es para CI/CD público
# GoogleService-Info.plist
ios/Pods/
ios/Flutter/App.framework
ios/Flutter/Flutter.framework
ios/Flutter/Generated.xcconfig
ios/Flutter/flutter_export_environment.sh
ios/.symlinks/
ios/Runner/GeneratedPluginRegistrant.*

# macOS
macos/Flutter/GeneratedPluginRegistrant.swift
macos/Flutter/Generated.xcconfig
macos/Flutter/flutter_export_environment.sh
macos/.symlinks/

# Linux
linux/flutter/generated_plugin_registrant.h
linux/flutter/generated_plugin_registrant.cc
linux/flutter/generated_plugins.cmake
linux/.elfCache/
linux/flutter/ephemeral/

# Windows
windows/flutter/generated_plugin_registrant.h
windows/flutter/generated_plugin_registrant.cc
windows/flutter/generated_plugins.cmake
windows/flutter/ephemeral/
windows/.vs/

# Archivos de prueba y cobertura
coverage/
test/.test_output/
test/**/.test_output/

# Archivos sensibles (añade los tuyos si es necesario)
# .env
# secrets.json

# Web (si estás construyendo para web, la carpeta build/web ya está cubierta por build/)
# build/web/