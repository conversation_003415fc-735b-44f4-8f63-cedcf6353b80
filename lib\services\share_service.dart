import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:share_plus/share_plus.dart';
import '../models/bot_model.dart';
import '../models/history_model.dart';
import '../utils/response_formatter.dart';
import '../utils/csv_generator.dart';

class ShareService {
  static void copyToClipboard(String text, BuildContext context) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Texto copiado al portapapeles')),
    );
  }

  static Future<void> shareText(String text, String botName) async {
    try {
      await SharePlus.instance.share(
        ShareParams(text: text, subject: 'Respuesta de $botName'),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error al compartir texto: $e');
      }
      rethrow;
    }
  }

  static Future<void> shareTableAsCSV(
    ChatHistory message,
    Bot bot,
    BuildContext context,
  ) async {
    try {
      // Mostrar indicador de carga
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 16),
                Text('Generando archivo CSV...'),
              ],
            ),
            duration: Duration(seconds: 2),
          ),
        );
      }

      // Obtener los datos formateados
      final List<Map<String, dynamic>> data = ResponseFormatter.formatResponse(
        message,
      );

      if (data.isEmpty) {
        throw Exception('No hay datos para exportar');
      }

      // Generar nombre del archivo
      final fileName = CsvGenerator.generateFileName(
        botName: bot.name,
        messageDate: message.fechaRegistro,
        customName: 'tabla_datos',
      );

      // Generar archivo CSV
      final csvFile = await CsvGenerator.generateCsvFile(
        data: data,
        fileName: fileName,
      );

      // Compartir el archivo
      await SharePlus.instance.share(
        ShareParams(
          text: 'Datos exportados desde ${bot.name}',
          files: [XFile(csvFile.path)],
        ),
      );

      // Mostrar confirmación
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Archivo CSV compartido: ${data.length} filas exportadas',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error al compartir CSV: $e');
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error al generar CSV: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      rethrow;
    }
  }
}
