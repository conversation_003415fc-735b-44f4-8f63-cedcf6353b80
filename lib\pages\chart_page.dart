import 'package:flutter/material.dart';
import '../models/history_model.dart';
import '../models/chart_models.dart';
import '../services/chart_service.dart';
import '../widgets/charts/grouped_bar_chart.dart';
import '../widgets/charts/line_chart.dart';
import '../widgets/charts/heat_map_chart.dart';
import '../widgets/profile_drawer.dart';
import '../widgets/profile_avatar_button.dart';

class ChartPage extends StatefulWidget {
  final ChatHistory message;

  const ChartPage({super.key, required this.message});

  @override
  State<ChartPage> createState() => _ChartPageState();
}

class _ChartPageState extends State<ChartPage> with TickerProviderStateMixin {
  late TabController _tabController;
  ProcessedChartData? _chartData;
  List<String> _availableColumns = [];
  List<String> _availableMetrics = [];
  List<String> _availableDateColumns = [];
  List<int> _availableYears = [];
  List<int> _availableMonths = [];
  String? _selectedGroupBy;
  List<String> _selectedMetrics = [];
  String? _selectedDateColumn;
  int? _selectedYear;
  int? _selectedMonth;
  bool _isLoading = true;

  // Variables para comparación
  bool _isComparisonEnabled = false;
  int? _comparisonYear;
  ProcessedChartData? _comparisonChartData;

  // Variables específicas para mapa de calor
  String? _selectedVerticalGroupBy;
  String? _selectedHorizontalGroupBy;
  String? _selectedHeatMetric;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // Rebuild para actualizar controles según pestaña activa
    });
    _initializeData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    setState(() => _isLoading = true);

    try {
      // Obtener columnas y métricas disponibles
      _availableColumns = ChartService.getAvailableGroupByColumns(
        widget.message,
      );
      _availableMetrics = ChartService.getAvailableMetrics(widget.message);
      _availableDateColumns = ChartService.getAvailableDateColumns(
        widget.message,
      );

      // Configurar valores por defecto
      if (_availableColumns.isNotEmpty) {
        _selectedGroupBy = _availableColumns.first;
        _selectedVerticalGroupBy = _availableColumns.first;
        _selectedHorizontalGroupBy = _availableColumns.length > 1
            ? _availableColumns[1]
            : _availableColumns.first;
      }
      if (_availableMetrics.isNotEmpty) {
        _selectedMetrics = _availableMetrics.take(2).toList();
        _selectedHeatMetric = _availableMetrics.first;
      }

      // Configurar filtros de fecha por defecto
      if (_availableDateColumns.isNotEmpty) {
        _selectedDateColumn = _availableDateColumns.first;
        _availableYears = ChartService.getAvailableYears(
          widget.message,
          _selectedDateColumn!,
        );
        if (_availableYears.isNotEmpty) {
          _selectedYear = _availableYears.last; // Año más reciente por defecto
          _availableMonths = ChartService.getAvailableMonths(
            widget.message,
            _selectedDateColumn!,
            _selectedYear!,
          );
        }
      }

      // Procesar datos iniciales
      _processChartData();
    } catch (e) {
      debugPrint('Error initializing chart data: $e');
      _chartData = ProcessedChartData.empty();
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _processChartData() {
    // Validar datos básicos para gráficos de barras y líneas
    final hasBasicData =
        _selectedGroupBy != null && _selectedMetrics.isNotEmpty;

    // Validar datos específicos para mapa de calor
    final hasHeatMapData =
        _selectedVerticalGroupBy != null &&
        _selectedHorizontalGroupBy != null &&
        _selectedHeatMetric != null &&
        _selectedVerticalGroupBy != _selectedHorizontalGroupBy;

    if (hasBasicData || hasHeatMapData) {
      _chartData = ChartService.processChartData(
        message: widget.message,
        selectedGroupBy: _selectedGroupBy,
        selectedMetrics: _selectedMetrics,
        selectedDateColumn: _selectedDateColumn,
        selectedYear: _selectedYear,
        selectedMonth: _selectedMonth,
        // Parámetros específicos para mapa de calor
        selectedVerticalGroupBy: _selectedVerticalGroupBy,
        selectedHorizontalGroupBy: _selectedHorizontalGroupBy,
        selectedHeatMetric: _selectedHeatMetric,
      );

      // Procesar datos de comparación si está habilitada
      if (_isComparisonEnabled && _comparisonYear != null && hasBasicData) {
        _comparisonChartData = ChartService.processChartData(
          message: widget.message,
          selectedGroupBy: _selectedGroupBy,
          selectedMetrics: _selectedMetrics,
          selectedDateColumn: _selectedDateColumn,
          selectedYear: _comparisonYear,
          selectedMonth: _selectedMonth,
          // Parámetros específicos para mapa de calor (no se usan en comparación)
          selectedVerticalGroupBy: _selectedVerticalGroupBy,
          selectedHorizontalGroupBy: _selectedHorizontalGroupBy,
          selectedHeatMetric: _selectedHeatMetric,
        );
      } else {
        _comparisonChartData = null;
      }
    } else {
      _chartData = ProcessedChartData.empty();
      _comparisonChartData = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Gráficas'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.bar_chart), text: 'Barras'),
            Tab(icon: Icon(Icons.show_chart), text: 'Líneas'),
            Tab(icon: Icon(Icons.grid_on), text: 'Calor'),
          ],
        ),
        actions: const [ProfileAvatarButton()],
      ),
      endDrawer: const ProfileDrawer(),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Column(
                children: [
                  // Panel de configuración
                  _buildQuickConfigPanel(theme),

                  // Área de gráficas con altura fija
                  SizedBox(
                    height: 400, // Altura fija para la gráfica
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildBarChart(),
                        _buildLineChart(),
                        _buildHeatChart(),
                      ],
                    ),
                  ),

                  // Espacio adicional al final
                  const SizedBox(height: 20),
                ],
              ),
            ),
    );
  }

  Widget _buildQuickConfigPanel(ThemeData theme) {
    final isHeatMapTab = _tabController.index == 2; // Pestaña de mapa de calor

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Filtros de Fecha
          if (_availableDateColumns.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildDateFilters(theme),
          ],

          const SizedBox(height: 16),

          // Controles específicos según la pestaña
          if (isHeatMapTab) ...[
            _buildHeatMapControls(theme),
          ] else ...[
            _buildGroupByDropdown(theme),
            const SizedBox(height: 16),
            _buildMetricsSelector(theme),
          ],

          const SizedBox(height: 12),
        ],
      ),
    );
  }

  Widget _buildGroupByDropdown(ThemeData theme) {
    // Ordenar las columnas alfabéticamente
    final sortedColumns = List<String>.from(_availableColumns)..sort();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Agrupar por:', style: theme.textTheme.bodySmall),
        const SizedBox(height: 4),
        DropdownButtonFormField<String>(
          value: _selectedGroupBy,
          decoration: const InputDecoration(
            isDense: true,
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: sortedColumns.map((column) {
            return DropdownMenuItem(
              value: column,
              child: Text(
                column,
                style: theme.textTheme.bodySmall,
                overflow: TextOverflow.ellipsis,
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedGroupBy = value;
              _processChartData();
            });
          },
        ),
      ],
    );
  }

  Widget _buildMetricsSelector(ThemeData theme) {
    // Ordenar las métricas alfabéticamente
    final sortedMetrics = List<String>.from(_availableMetrics)..sort();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Métricas:', style: theme.textTheme.bodySmall),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: sortedMetrics.map((metric) {
            final isSelected = _selectedMetrics.contains(metric);
            return FilterChip(
              label: Text(
                metric,
                style: TextStyle(
                  fontSize: 12,
                  color: isSelected
                      ? theme.colorScheme.onPrimary
                      : theme.colorScheme.onSurface,
                ),
              ),
              selected: isSelected,
              onSelected: (bool selected) {
                setState(() {
                  if (selected) {
                    _selectedMetrics.add(metric);
                  } else {
                    _selectedMetrics.remove(metric);
                  }
                  _processChartData();
                });
              },
              selectedColor: theme.colorScheme.primary,
              backgroundColor: theme.colorScheme.surfaceContainerHighest,
              checkmarkColor: theme.colorScheme.onPrimary,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildHeatMapControls(ThemeData theme) {
    // Ordenar las columnas y métricas alfabéticamente
    final sortedColumns = List<String>.from(_availableColumns)..sort();
    final sortedMetrics = List<String>.from(_availableMetrics)..sort();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Agrupador Vertical
        Text('Agrupador Vertical (Filas):', style: theme.textTheme.bodySmall),
        const SizedBox(height: 4),
        DropdownButtonFormField<String>(
          value: _selectedVerticalGroupBy,
          decoration: const InputDecoration(
            isDense: true,
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: sortedColumns.map((column) {
            return DropdownMenuItem(
              value: column,
              child: Text(
                column,
                style: theme.textTheme.bodySmall,
                overflow: TextOverflow.ellipsis,
              ),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedVerticalGroupBy = value;
              // Si el agrupador horizontal es igual, cambiarlo
              if (_selectedHorizontalGroupBy == value &&
                  _availableColumns.length > 1) {
                _selectedHorizontalGroupBy = _availableColumns.firstWhere(
                  (col) => col != value,
                  orElse: () => _availableColumns.first,
                );
              }
              _processChartData();
            });
          },
        ),

        const SizedBox(height: 16),

        // Agrupador Horizontal
        Text(
          'Agrupador Horizontal (Columnas):',
          style: theme.textTheme.bodySmall,
        ),
        const SizedBox(height: 4),
        DropdownButtonFormField<String>(
          value: _selectedHorizontalGroupBy,
          decoration: const InputDecoration(
            isDense: true,
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: sortedColumns
              .where((column) => column != _selectedVerticalGroupBy)
              .map((column) {
                return DropdownMenuItem(
                  value: column,
                  child: Text(
                    column,
                    style: theme.textTheme.bodySmall,
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              })
              .toList(),
          onChanged: (value) {
            setState(() {
              _selectedHorizontalGroupBy = value;
              _processChartData();
            });
          },
        ),

        const SizedBox(height: 16),

        // Métrica única con chips
        Row(
          children: [
            Text('Métrica:', style: theme.textTheme.bodySmall),
            const SizedBox(width: 8),
            Text(
              '(selecciona una)',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                fontSize: 10,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: sortedMetrics.map((metric) {
            final isSelected = _selectedHeatMetric == metric;
            return FilterChip(
              label: Text(
                metric,
                style: TextStyle(
                  fontSize: 12,
                  color: isSelected
                      ? theme.colorScheme.onPrimary
                      : theme.colorScheme.onSurface,
                ),
              ),
              selected: isSelected,
              onSelected: (bool selected) {
                setState(() {
                  // Solo permitir seleccionar una métrica a la vez
                  if (selected && _selectedHeatMetric != metric) {
                    // Cambiar a la nueva métrica seleccionada
                    _selectedHeatMetric = metric;
                    _processChartData();
                  }
                  // Si ya está seleccionada o se intenta deseleccionar, no hacer nada
                });
              },
              selectedColor: theme.colorScheme.primary,
              backgroundColor: theme.colorScheme.surfaceContainerHighest,
              checkmarkColor: theme.colorScheme.onPrimary,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildBarChart() {
    if (_chartData?.bar.isEmpty ?? true) {
      return const Center(
        child: Text(
          'No hay datos suficientes para mostrar la gráfica de barras',
        ),
      );
    }

    // Combinar datos con comparación si está habilitada
    final chartData =
        _isComparisonEnabled &&
            _comparisonChartData != null &&
            _comparisonYear != null
        ? ChartService.combineWithComparisonData(
            _chartData!.bar,
            _comparisonChartData!.bar,
            _comparisonYear!,
          )
        : _chartData!.bar;

    // Calcular ancho dinámico basado en la cantidad de datos
    final screenWidth = MediaQuery.of(context).size.width;
    final dataCount = chartData.labels.length;
    final metricsCount = chartData.datasets.length;

    // Ancho base por grupo de barras + espacio para múltiples métricas
    const baseWidthPerGroup = 60.0;
    const widthPerMetric = 25.0;
    final groupWidth = baseWidthPerGroup + (metricsCount * widthPerMetric);
    final calculatedWidth = (dataCount * groupWidth).clamp(
      screenWidth - 32, // Ancho mínimo (pantalla completa)
      double.infinity, // Sin límite máximo para permitir scroll
    );

    return Padding(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: GroupedBarChart(
          data: chartData,
          width: calculatedWidth,
          height: 350, // Altura fija para la gráfica
          showValuesOnTopOfBars: true,
          horizontalLabelRotation: -45, // Rotar etiquetas 45 grados
        ),
      ),
    );
  }

  Widget _buildLineChart() {
    if (_chartData?.line.isEmpty ?? true) {
      return const Center(
        child: Text(
          'No hay datos suficientes para mostrar la gráfica de líneas',
        ),
      );
    }

    // Combinar datos con comparación si está habilitada
    final chartData =
        _isComparisonEnabled &&
            _comparisonChartData != null &&
            _comparisonYear != null
        ? ChartService.combineWithComparisonData(
            _chartData!.line,
            _comparisonChartData!.line,
            _comparisonYear!,
          )
        : _chartData!.line;

    // Calcular ancho dinámico basado en la cantidad de datos
    final screenWidth = MediaQuery.of(context).size.width;
    final dataCount = chartData.labels.length;

    // Ancho base por punto de datos
    const baseWidthPerPoint = 80.0;
    final calculatedWidth = (dataCount * baseWidthPerPoint).clamp(
      screenWidth - 32, // Ancho mínimo (pantalla completa)
      double.infinity, // Sin límite máximo para permitir scroll
    );

    return Padding(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: LineChart(
          data: chartData,
          width: calculatedWidth,
          height: 350, // Altura fija para la gráfica
          showDataPoints: true,
          showDataValues: true,
          horizontalLabelRotation: -45, // Rotar etiquetas 45 grados
        ),
      ),
    );
  }

  Widget _buildHeatChart() {
    if (_chartData?.heat == null || _chartData!.heat!.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.grid_on, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No hay datos suficientes para mostrar el mapa de calor'),
          ],
        ),
      );
    }

    // Calcular ancho dinámico basado en la cantidad de datos
    final screenWidth = MediaQuery.of(context).size.width;
    final horizontalLabelsCount = _chartData!.heat!.horizontalLabels.length;

    // Ancho base por celda + espacio para etiquetas (con más padding para centrado)
    const baseCellSize = 20.0;
    const cellSpacing = 4.0;
    const verticalLabelWidth = 80.0;
    const horizontalPadding = 60.0; // Aumentado para mejor centrado
    const extraPaddingForLabels = 40.0; // Espacio extra para etiquetas rotadas

    final calculatedWidth =
        (horizontalLabelsCount * (baseCellSize + cellSpacing) +
                verticalLabelWidth +
                horizontalPadding +
                extraPaddingForLabels)
            .clamp(
              screenWidth - 32, // Ancho mínimo (pantalla completa)
              double.infinity, // Sin límite máximo para permitir scroll
            );

    return Padding(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: HeatMapChart(
          data: _chartData!.heat!,
          width: calculatedWidth,
          height: 350, // Altura fija para la gráfica
          onCellTap: (vertical, horizontal, value) {
            // Mostrar información de la celda seleccionada
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  '$vertical × $horizontal: ${value.toStringAsFixed(1)}',
                ),
                duration: const Duration(seconds: 2),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildDateFilters(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Filtros de Fecha:',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 6),

        // Selector de columna de fecha (si hay múltiples)
        if (_availableDateColumns.length > 1) ...[
          Text('Campo de fecha:', style: theme.textTheme.bodySmall),
          const SizedBox(height: 4),
          DropdownButtonFormField<String>(
            value: _selectedDateColumn,
            decoration: const InputDecoration(
              isDense: true,
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            items: (List<String>.from(_availableDateColumns)..sort()).map((
              column,
            ) {
              return DropdownMenuItem(
                value: column,
                child: Text(
                  column,
                  style: theme.textTheme.bodySmall,
                  overflow: TextOverflow.ellipsis,
                ),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedDateColumn = value;
                if (value != null) {
                  _availableYears = ChartService.getAvailableYears(
                    widget.message,
                    value,
                  );
                  _selectedYear = _availableYears.isNotEmpty
                      ? _availableYears.last
                      : null;
                  if (_selectedYear != null) {
                    _availableMonths = ChartService.getAvailableMonths(
                      widget.message,
                      value,
                      _selectedYear!,
                    );
                    _selectedMonth = null; // Reset month selection
                  }
                  _processChartData();
                }
              });
            },
          ),
          const SizedBox(height: 8),
        ],

        // Selectores de año y mes en fila
        Row(
          children: [
            // Selector de año
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Año:', style: theme.textTheme.bodySmall),
                  const SizedBox(height: 4),
                  DropdownButtonFormField<int?>(
                    value: _selectedYear,
                    decoration: const InputDecoration(
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    items: [
                      DropdownMenuItem<int?>(
                        value: null,
                        child: Text('Todos', style: theme.textTheme.bodySmall),
                      ),
                      ..._availableYears.map((year) {
                        return DropdownMenuItem(
                          value: year,
                          child: Text(
                            year.toString(),
                            style: theme.textTheme.bodySmall,
                          ),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedYear = value;
                        if (value != null && _selectedDateColumn != null) {
                          _availableMonths = ChartService.getAvailableMonths(
                            widget.message,
                            _selectedDateColumn!,
                            value,
                          );
                        } else {
                          _availableMonths = [];
                        }
                        _selectedMonth = null; // Reset month selection
                        _processChartData();
                      });
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),

            // Selector de mes
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Mes:', style: theme.textTheme.bodySmall),
                  const SizedBox(height: 4),
                  DropdownButtonFormField<int?>(
                    value: _selectedMonth,
                    decoration: const InputDecoration(
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    items: [
                      DropdownMenuItem<int?>(
                        value: null,
                        child: Text('Todos', style: theme.textTheme.bodySmall),
                      ),
                      ..._availableMonths.map((month) {
                        return DropdownMenuItem(
                          value: month,
                          child: Text(
                            _getMonthName(month),
                            style: theme.textTheme.bodySmall,
                          ),
                        );
                      }),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedMonth = value;
                        _processChartData();
                      });
                    },
                  ),
                ],
              ),
            ),
          ],
        ),

        // Botón de resetear filtros
        const SizedBox(height: 8),
        Row(
          children: [
            OutlinedButton(
              onPressed: _resetDateFilters,
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                minimumSize: const Size(0, 36),
              ),
              child: Text('Resetear filtros', style: theme.textTheme.bodySmall),
            ),

            // Botón de comparación (solo si hay 2 o más años disponibles y no estamos en la pestaña de mapa de calor)
            if (_availableYears.length >= 2 && _tabController.index != 2) ...[
              const SizedBox(width: 12),
              _isComparisonEnabled
                  ? ElevatedButton.icon(
                      onPressed: _disableComparison,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        minimumSize: const Size(0, 36),
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: theme.colorScheme.onPrimary,
                      ),
                      icon: const Icon(Icons.compare_arrows, size: 16),
                      label: Text(
                        'Comparando ${_comparisonYear ?? ''}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onPrimary,
                        ),
                      ),
                    )
                  : OutlinedButton.icon(
                      onPressed: _enableComparison,
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        minimumSize: const Size(0, 36),
                      ),
                      icon: const Icon(Icons.compare_arrows, size: 16),
                      label: Text(
                        'Comparación',
                        style: theme.textTheme.bodySmall,
                      ),
                    ),
            ],
          ],
        ),
      ],
    );
  }

  String _getMonthName(int month) {
    const monthNames = [
      'Enero',
      'Febrero',
      'Marzo',
      'Abril',
      'Mayo',
      'Junio',
      'Julio',
      'Agosto',
      'Septiembre',
      'Octubre',
      'Noviembre',
      'Diciembre',
    ];
    return monthNames[month - 1];
  }

  void _resetDateFilters() {
    setState(() {
      _selectedYear = null;
      _selectedMonth = null;
      // Deshabilitar comparación al resetear filtros
      _isComparisonEnabled = false;
      _comparisonYear = null;
      _comparisonChartData = null;
      _processChartData();
    });
  }

  void _enableComparison() {
    // Mostrar diálogo para seleccionar año de comparación
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Seleccionar año para comparación'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Selecciona el año que deseas comparar:'),
              const SizedBox(height: 16),
              DropdownButtonFormField<int>(
                value: null,
                decoration: const InputDecoration(
                  labelText: 'Año de comparación',
                  border: OutlineInputBorder(),
                ),
                items: _availableYears
                    .where((year) => year != _selectedYear)
                    .map((year) {
                      return DropdownMenuItem(
                        value: year,
                        child: Text(year.toString()),
                      );
                    })
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    Navigator.of(context).pop();
                    setState(() {
                      _isComparisonEnabled = true;
                      _comparisonYear = value;
                      _processChartData();
                    });
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancelar'),
            ),
          ],
        );
      },
    );
  }

  void _disableComparison() {
    setState(() {
      _isComparisonEnabled = false;
      _comparisonYear = null;
      _comparisonChartData = null;
      _processChartData();
    });
  }
}
